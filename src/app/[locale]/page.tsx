import {
    ApplicationScenarios,
    CompanyStrength,
    Hero,
    ModularSystem,
    ProductPortfolio,
    ProfessionalConsultation,
} from '@/components/home';
import { Suspense } from 'react';

interface HomePageProps {
  params: { locale: string };
}

export default function HomePage({ params: { locale } }: HomePageProps) {
  return (
    <>
      {/* Hero Section - 英雄区域，专业定位和核心CTA */}
      <Suspense fallback={<div className='h-screen' />}>
        <Hero locale={locale} />
      </Suspense>

      {/* Product Portfolio - 核心产品矩阵，展示三大产品系列 */}
      <Suspense fallback={<div className='h-96' />}>
        <ProductPortfolio />
      </Suspense>

      {/* Modular System - 模块化防汛系统，三层防护体系 */}
      <Suspense fallback={<div className='h-96' />}>
        <ModularSystem />
      </Suspense>

      {/* Application Scenarios - 广泛应用场景，证明产品适应性 */}
      <Suspense fallback={<div className='h-96' />}>
        <ApplicationScenarios />
      </Suspense>

      {/* Company Strength - 企业实力展示，技术实力与全球布局 */}
      <Suspense fallback={<div className='h-96' />}>
        <CompanyStrength />
      </Suspense>

      {/* Professional Consultation - 专业咨询与技术支持，包含Modal表单 */}
      <Suspense fallback={<div className='h-96' />}>
        <ProfessionalConsultation />
      </Suspense>
    </>
  );
}
