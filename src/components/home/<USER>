'use client';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Balancer } from '@/components/ui/balancer';
import { Button } from '@/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { AlertCircle, CheckCircle, Clock, MessageSquare } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

export const ProfessionalConsultation = () => {
  const t = useTranslations('web.home.professionalConsultation');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [isOpen, setIsOpen] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise((resolve) => setTimeout(resolve, 2000));

    setIsSubmitting(false);
    setSubmitStatus('success');

    // Reset status after 3 seconds and close modal
    setTimeout(() => {
      setSubmitStatus('idle');
      setIsOpen(false);
    }, 3000);
  };

  return (
    <div className='w-full py-16 lg:py-28'>
      <div className='container mx-auto'>
        <div className='flex flex-col gap-10'>
          <div className='flex flex-col items-start gap-4'>
            <div className='flex flex-col gap-2'>
              <h2 className='max-w-xl text-left font-regular text-3xl tracking-tighter md:text-5xl'>
                <Balancer>{t('title')}</Balancer>
              </h2>
              <p className='max-w-xl text-left text-lg text-muted-foreground leading-relaxed tracking-tight lg:max-w-lg'>
                {t('description')}
              </p>
            </div>
          </div>

          {/* Consultation Services */}
          <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3'>
            {[0, 1, 2, 3, 4].map((index) => (
              <div
                key={index}
                className='flex items-center gap-3 rounded-lg bg-muted p-4'
              >
                <CheckCircle className='h-5 w-5 text-primary flex-shrink-0' />
                <p className='text-sm text-muted-foreground'>
                  {t(`services.${index}`)}
                </p>
              </div>
            ))}
          </div>

          {/* CTA Section */}
          <div className='flex flex-col items-center gap-6 rounded-lg bg-primary/5 p-8 border border-primary/20'>
            <div className='flex items-center gap-3'>
              <MessageSquare className='h-6 w-6 text-primary' />
              <h3 className='text-xl font-semibold text-center'>
                {t('cta')}
              </h3>
            </div>

            <Dialog open={isOpen} onOpenChange={setIsOpen}>
              <DialogTrigger asChild>
                <Button size='lg' className='gap-2'>
                  {t('cta')} <MessageSquare className='h-4 w-4' />
                </Button>
              </DialogTrigger>
              <DialogContent className='sm:max-w-md'>
                <DialogHeader>
                  <DialogTitle>{t('form.title')}</DialogTitle>
                  <DialogDescription>
                    {t('description')}
                  </DialogDescription>
                </DialogHeader>

                {submitStatus === 'success' && (
                  <Alert className='mb-4'>
                    <CheckCircle className='h-4 w-4' />
                    <AlertDescription>
                      感谢您的咨询！我们的专业团队将在24小时内与您联系。
                    </AlertDescription>
                  </Alert>
                )}

                {submitStatus === 'error' && (
                  <Alert variant='destructive' className='mb-4'>
                    <AlertCircle className='h-4 w-4' />
                    <AlertDescription>
                      提交失败，请稍后重试。
                    </AlertDescription>
                  </Alert>
                )}

                <form onSubmit={handleSubmit} className='space-y-4'>
                  <div className='space-y-2'>
                    <Label htmlFor='name'>
                      {t('form.name')} <span className='text-red-500'>*</span>
                    </Label>
                    <Input
                      id='name'
                      name='name'
                      required
                      placeholder={t('form.placeholders.name')}
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='email'>
                      {t('form.email')} <span className='text-red-500'>*</span>
                    </Label>
                    <Input
                      id='email'
                      name='email'
                      type='email'
                      required
                      placeholder={t('form.placeholders.email')}
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='company'>
                      {t('form.company')} <span className='text-muted-foreground'>({t('form.optional')})</span>
                    </Label>
                    <Input
                      id='company'
                      name='company'
                      placeholder={t('form.placeholders.company')}
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='region'>
                      {t('form.region')} <span className='text-muted-foreground'>({t('form.optional')})</span>
                    </Label>
                    <Input
                      id='region'
                      name='region'
                      placeholder={t('form.placeholders.region')}
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='requirements'>
                      {t('form.requirements')} <span className='text-muted-foreground'>({t('form.optional')})</span>
                    </Label>
                    <Textarea
                      id='requirements'
                      name='requirements'
                      placeholder={t('form.placeholders.requirements')}
                      className='min-h-[80px]'
                    />
                  </div>

                  <div className='flex items-center justify-between pt-4'>
                    <div className='flex items-center gap-2 text-sm text-muted-foreground'>
                      <Clock className='h-4 w-4' />
                      <span>{t('supportInfo')}</span>
                    </div>
                    <Button
                      type='submit'
                      disabled={isSubmitting}
                      className='min-w-[100px]'
                    >
                      {isSubmitting ? '提交中...' : t('form.submit')}
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </div>
    </div>
  );
};
