import { Badge } from '@/components/ui/badge';
import { Balancer } from '@/components/ui/balancer';
import { CheckCircle2, Droplets, Layers, Quote, Shield, Star, TrendingUp, Users } from 'lucide-react';
import { useTranslations } from 'next-intl';

export const ModularSystem = () => {
  const t = useTranslations('web.home.modularSystem');

  const layerIcons = [Layers, Shield, Droplets];
  const layerColors = [
    'from-blue-500/10 to-blue-600/10 border-blue-200/50',
    'from-orange-500/10 to-orange-600/10 border-orange-200/50',
    'from-green-500/10 to-green-600/10 border-green-200/50'
  ];

  return (
    <div className='w-full py-16 lg:py-24 bg-muted/30'>
      <div className='container mx-auto'>
        <div className='flex flex-col gap-12'>
          {/* Header Section */}
          <div className='flex flex-col items-center text-center gap-6'>
            <div className='flex flex-col gap-4'>
              <h2 className='max-w-4xl text-center font-regular text-3xl tracking-tighter md:text-5xl'>
                <Balancer>{t('title')}</Balancer>
              </h2>
            </div>
          </div>

          {/* Industry Insight */}
          <div className='relative overflow-hidden rounded-xl bg-gradient-to-r from-primary/5 to-primary/10 p-8 border border-primary/20'>
            <div className='flex items-start gap-4'>
              <div className='flex h-12 w-12 items-center justify-center rounded-xl bg-primary/10 flex-shrink-0'>
                <Quote className='h-6 w-6 text-primary' />
              </div>
              <div className='flex flex-col gap-2'>
                <h3 className='text-lg font-semibold text-foreground'>
                  {t('insightTitle')}
                </h3>
                <p className='text-lg text-muted-foreground leading-relaxed italic'>
                  "{t('insight')}"
                </p>
              </div>
            </div>
            {/* Background Pattern */}
            <div className='absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary/5 to-transparent rounded-full -translate-y-16 translate-x-16'></div>
          </div>

          {/* Subtitle */}
          <div className='flex flex-col items-center text-center gap-4'>
            <p className='text-lg text-foreground leading-relaxed max-w-3xl'>
              {t('subtitle')}
            </p>
          </div>

          {/* Three Layer Protection System */}
          <div className='grid grid-cols-1 gap-8 md:grid-cols-3'>
            {[0, 1, 2].map((index) => {
              const Icon = layerIcons[index];
              const colorClass = layerColors[index];

              return (
                <div
                  key={index}
                  className={`group relative overflow-hidden rounded-xl bg-gradient-to-br ${colorClass} border p-8 transition-all duration-300 hover:shadow-lg hover:scale-[1.02]`}
                >
                  {/* Layer Header */}
                  <div className='flex items-center gap-3 mb-6'>
                    <div className='flex h-12 w-12 items-center justify-center rounded-xl bg-background/80 backdrop-blur-sm'>
                      <Icon className='h-6 w-6 text-primary' />
                    </div>
                    <div>
                      <h3 className='text-lg font-semibold tracking-tight'>
                        {t(`layers.${index}.title`)}
                      </h3>
                      <Badge variant="secondary" className='mt-1 text-xs'>
                        {t('protectionLevel')} {index + 1}
                      </Badge>
                    </div>
                  </div>

                  {/* Layer Details */}
                  <div className='space-y-4'>
                    <div className='space-y-2'>
                      <div className='flex items-center gap-2'>
                        <CheckCircle2 className='h-4 w-4 text-primary' />
                        <p className='text-sm font-semibold text-foreground'>{t('coreProducts')}</p>
                      </div>
                      <p className='text-sm text-muted-foreground leading-relaxed pl-6'>
                        {t(`layers.${index}.products`)}
                      </p>
                    </div>

                    <div className='space-y-2'>
                      <div className='flex items-center gap-2'>
                        <Users className='h-4 w-4 text-blue-600' />
                        <p className='text-sm font-semibold text-foreground'>{t('applicationScenario')}</p>
                      </div>
                      <p className='text-sm text-muted-foreground leading-relaxed pl-6'>
                        {t(`layers.${index}.scenario`)}
                      </p>
                    </div>

                    <div className='space-y-2'>
                      <div className='flex items-center gap-2'>
                        <TrendingUp className='h-4 w-4 text-green-600' />
                        <p className='text-sm font-semibold text-foreground'>{t('valueAdvantage')}</p>
                      </div>
                      <p className='text-sm text-muted-foreground leading-relaxed pl-6'>
                        {t(`layers.${index}.value`)}
                      </p>
                    </div>

                    {/* Market Feedback - only for first two layers */}
                    {index < 2 && (
                      <div className='space-y-2'>
                        <div className='flex items-center gap-2'>
                          <Star className='h-4 w-4 text-yellow-600' />
                          <p className='text-sm font-semibold text-foreground'>{t('marketFeedback')}</p>
                        </div>
                        <p className='text-sm text-muted-foreground leading-relaxed pl-6'>
                          {t(`layers.${index}.feedback`)}
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Hover Effect Overlay */}
                  <div className='absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none'></div>
                </div>
              );
            })}
          </div>


        </div>
      </div>
    </div>
  );
};
